name: Install, Build & Test

on:
  pull_request:
    branches:
      - master

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17 (Amazon Corretto)
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'
          server-id: github
          settings-path: ${{ github.workspace }}

      - name: Build with Gradle
        run: gradle :clean :build


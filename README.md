# 2 Factor Authentication

> [!NOTE]\
> The plugin is currently undergoing significant changes and improvements for version 2.0. Stay tuned for exciting updates!

![2FA Logo](https://i.imgur.com/kS8RauH.png)


[![License](https://img.shields.io/badge/License-MIT-%23ff7057?style=flat)](https://github.com/LielAmar/2FA/blob/master/LICENSE)
[![Release](https://img.shields.io/badge/dynamic/json?color=blue&label=Latest%20Release&query=tag_name&url=https%3A%2F%2Fapi.github.com%2Frepos%2Flielamar%2F2fa%2Freleases%2Flatest)](https://github.com/LielAmar/2FA/releases/latest)
[![Discord](https://img.shields.io/discord/416652224505184276?color=%235865F2&label=Join%20Our%20Discord)](https://discord.gg/NzgBrqR)
[![Issues](https://img.shields.io/github/issues-raw/LielAmar/2FA?logo=github&logoColor=white)](https://github.com/LielAmar/2FA/issues)
[![Spigot](https://img.shields.io/badge/dynamic/json?color=yellow&label=Check%20it%20on%20Spigot&query=downloads&suffix=%20Downloads&url=https%3A%2F%2Fapi.spiget.org%2Fv2%2Fresources%2F85594)](https://www.spigotmc.org/resources/85594/)
<br>
![GitHub Workflow](https://github.com/lielamar/2fa/actions/workflows/pull_requests.yml/badge.svg)

## Information
2FA is a Spigot/Paper plugin, with BungeeCord support.

This plugin adds another layer of protection to your server, by allowing players to use 2FA with an Authenticator app,
<br>requiring them to enter a time-based generated code when loggin in/changing ips.

2FA uses BungeeCord PluginMessagingChannel to communicate between the Spigot Servers & BungeeCord server, therefore, if you want
<br>to use 2FA for BungeeCord, you need to put the jar file in /plugins/ for every Spigot server, as well as for your BungeeCord server.

## Features
* 2FA Authentication, based on permissions.
* Ability to remove own/other's 2FA
* BungeeCord support
* Json, MySQL, MongoDB support
* Admin commands

## Using 2FA
You can download 2FA from [Spigot](https://www.spigotmc.org/resources/2-factor-authentication-bungeecord-json-mysql-mongodb.85594/).
<br>After downloading, simply drag it into your plugins folder and reload the server! <br>
* If you use BungeeCord, install the plugin in your BungeeCord/plugins/ folder as well!

## Statistics
For more statistics such as servers using the plugin, locations, server hardware and software etc., please visit [bStats](https://bstats.org/plugin/bukkit/Two%20Factor%20Authentication/9355). 

## Contributing
Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

## License
[MIT](https://choosealicense.com/licenses/mit/)

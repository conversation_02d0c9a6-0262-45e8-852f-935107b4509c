name: 2FA
version: "1.7.2"
api-version: 1.13
authors: [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>]
main: com.lielamar.auth.bukkit.TwoFactorAuthentication
description: Add another layer of protection to your server
softdepend: [PlaceholderAPI]

libraries:
  - commons-codec:commons-codec:1.17.1
  - com.zaxxer:HikariCP:5.1.0
  - com.h2database:h2:2.3.230
  - com.mysql:mysql-connector-j:9.0.0
  - org.mariadb.jdbc:mariadb-java-client:3.4.1
  - org.postgresql:postgresql:42.7.3
  - org.mongodb:mongodb-driver-sync:5.1.2
  - org.slf4j:slf4j-api:2.0.13
  - org.apache.logging.log4j:log4j-core:2.23.1

commands:
  2fa:
    description: Main 2FA Command
    aliases: [auth]

permissions:
  2fa.*:
    description: Permissions to all 2FA commands
    default: op
    children:
      2fa.use: true
      2fa.help: true
      2fa.remove: true
      2fa.remove.others: true
      2fa.reload: true
      2fa.alerts: true
  2fa.use:
    description: Permissions to use the /2FA command
    default: op
  2fa.help:
    description: Permissions to view the help page
    default: op
  2fa.remove:
    description: Permissions to remove your own 2FA
    default: op
  2fa.remove.others:
    description: Admin permissions to remove other players' 2FA
    default: op
  2fa.reload:
    description: Admin permissions to reload the 2FA plugin
    default: op
  2fa.alerts:
    description: Admin permissions to get alerts on critical issues with 2FA configuration
    default: op
  2fa.demand:
    description: A player with this permission must have 2FA linked to thier account
    default: false
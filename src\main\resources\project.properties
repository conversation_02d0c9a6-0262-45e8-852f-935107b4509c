load_maven_central=${repository.maven-central}

group_id_google_auth=${group-id.googleauth}
group_id_commons_codec=${group-id.commons-codec}
group_id_hikari_cp=${group-id.hikari-cp}
group_id_h2=${group-id.h2}
group_id_mysql=${group-id.mysql}
group_id_maria_db=${group-id.maria-db}
group_id_postgres=${group-id.postgres}
group_id_mongo_db=${group-id.mongo-db}
group_id_slf4j=${group-id.slf4j}

artifact_id_google_auth=${artifact-id.googleauth}
artifact_id_commons_codec=${artifact-id.commons-codec}
artifact_id_hikari_cp=${artifact-id.hikari-cp}
artifact_id_h2=${artifact-id.h2}
artifact_id_mysql=${artifact-id.mysql}
artifact_id_maria_db=${artifact-id.maria-db}
artifact_id_postgres=${artifact-id.postgres}
artifact_id_mongo_db=${artifact-id.mongo-db}
artifact_id_slf4j=${artifact-id.slf4j}

version_google_auth=${version.googleauth}
version_commons_codec=${version.commons-codec}
version_hikari_cp=${version.hikari-cp}
version_h2=${version.h2}
version_mysql=${version.mysql}
version_maria_db=${version.maria-db}
version_postgres=${version.postgres}
version_mongo_db=${version.mongo-db}
version_slf4j=${version.slf4j}